'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import VideoBlogForm from '@/components/video-blogs/VideoBlogForm';
import videoBlogService from '@/lib/api/videoBlogService';
import { VideoBlog } from '@/types/user';

export default function EditVideoBlogPage() {
  const router = useRouter();
  const params = useParams();
  const [videoBlog, setVideoBlog] = useState<VideoBlog | null>(null);
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const videoBlogId = params.id as string;

  useEffect(() => {
    const fetchVideoBlog = async () => {
      try {
        setFetchLoading(true);
        setError(null);
        
        const response = await videoBlogService.getVideoBlog(videoBlogId);
        
        if (response.status === 'success') {
          setVideoBlog(response.data.videoBlog);
        } else {
          throw new Error(response.message || 'Failed to fetch video blog');
        }
      } catch (error: any) {
        console.error('Error fetching video blog:', error);
        setError(error.message || 'Failed to fetch video blog');
      } finally {
        setFetchLoading(false);
      }
    };

    if (videoBlogId) {
      fetchVideoBlog();
    }
  }, [videoBlogId]);

  const handleSubmit = async (data: any) => {
    try {
      setLoading(true);
      
      const response = await videoBlogService.updateVideoBlog(videoBlogId, data);
      
      if (response.status === 'success') {
        // Show success message
        alert('Video blog updated successfully!');
        
        // Redirect to video blogs list
        router.push('/video-blogs');
      } else {
        throw new Error(response.message || 'Failed to update video blog');
      }
    } catch (error: any) {
      console.error('Error updating video blog:', error);
      alert('Failed to update video blog: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/video-blogs');
  };

  if (fetchLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full max-w-full overflow-x-hidden">
          <div className="space-y-6 p-4 md:p-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
                <div className="space-y-4">
                  <div className="h-10 bg-gray-200 rounded"></div>
                  <div className="h-24 bg-gray-200 rounded"></div>
                  <div className="h-10 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full max-w-full overflow-x-hidden">
          <div className="space-y-6 p-4 md:p-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                  <div className="mt-4 space-x-3">
                    <button
                      onClick={() => window.location.reload()}
                      className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Try Again
                    </button>
                    <button
                      onClick={handleCancel}
                      className="bg-white px-3 py-2 rounded-md text-sm font-medium text-gray-700 border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Go Back
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen w-full max-w-full overflow-x-hidden">
        <div className="space-y-6 p-4 md:p-6">
          {/* Header */}
          <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
              <div className="min-w-0 flex-1">
                <h2 className="text-xl md:text-2xl font-bold text-gray-900 break-words">
                  Edit Video Blog
                </h2>
                <p className="text-gray-600 mt-1 text-sm md:text-base break-words">
                  Update the video blog information and settings
                </p>
                {videoBlog && (
                  <p className="text-xs md:text-sm text-gray-500 mt-1">
                    Editing: {videoBlog.title}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Form */}
          {videoBlog && (
            <VideoBlogForm
              videoBlog={videoBlog}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              loading={loading}
            />
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}

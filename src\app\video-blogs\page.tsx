'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import VideoBlogTable from '@/components/video-blogs/VideoBlogTable';
import VideoBlogFilters from '@/components/video-blogs/VideoBlogFilters';
import videoBlogService from '@/lib/api/videoBlogService';
import { VideoBlogFilters as IVideoBlogFilters } from '@/types/user';
import { PlusIcon } from '@heroicons/react/24/outline';

export default function VideoBlogsPage() {
  const router = useRouter();
  const [videoBlogs, setVideoBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalVideoBlogs, setTotalVideoBlogs] = useState(0);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 12,
    hasNextPage: false,
    hasPrevPage: false,
  });

  const [filters, setFilters] = useState<IVideoBlogFilters>({
    page: 1,
    limit: 12,
    search: '',
    category: '',
    tag: '',
  });

  const fetchVideoBlogs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await videoBlogService.getVideoBlogs(filters);
      
      if (response.status === 'success') {
        setVideoBlogs(response.data.videoBlogs);
        setPagination(response.data.pagination);
        setTotalVideoBlogs(response.data.pagination.totalItems);
      } else {
        setError('Failed to fetch video blogs');
      }
    } catch (err: any) {
      console.error('Error fetching video blogs:', err);
      setError(err.message || 'Failed to fetch video blogs');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchVideoBlogs();
  }, [fetchVideoBlogs]);

  const handleFiltersChange = (newFilters: Partial<IVideoBlogFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page || 1, // Reset to page 1 when filters change (except for pagination)
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this video blog?')) {
      return;
    }

    try {
      await videoBlogService.deleteVideoBlog(id);
      // Refresh the list
      fetchVideoBlogs();
    } catch (err: any) {
      console.error('Error deleting video blog:', err);
      alert('Failed to delete video blog: ' + (err.message || 'Unknown error'));
    }
  };

  const handleAddNew = () => {
    router.push('/video-blogs/add');
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen w-full max-w-full overflow-x-hidden">
        <div className="space-y-6 p-4 md:p-6">
          {/* Header */}
          <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-start ">
              <div className="min-w-0 flex-1">
                <h2 className="text-xl md:text-2xl font-bold text-gray-900 break-words">
                  Video Blog Management
                </h2>
                <p className="text-gray-600 mt-1 text-sm md:text-base break-words">
                  Manage your video blog content with advanced filtering and search
                </p>
                {totalVideoBlogs > 0 && (
                  <p className="text-xs md:text-sm text-gray-500 mt-1">
                    {totalVideoBlogs} video blog{totalVideoBlogs !== 1 ? 's' : ''} total
                  </p>
                )}
              </div>
              <div className="flex-shrink-0">
                <button
                  onClick={handleAddNew}
                  className="inline-flex items-center px-3 py-2 md:px-4 md:py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <PlusIcon className="w-4 h-4 mr-1 md:mr-2" />
                  <span className="text-xs md:text-sm">Add Video Blog</span>
                </button>
              </div>
            </div>
          </div>

          {/* Filters */}
          <VideoBlogFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            loading={loading}
          />

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                  <div className="mt-4">
                    <button
                      onClick={fetchVideoBlogs}
                      className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Video Blogs Table */}
          {!error && (
            <VideoBlogTable
              videoBlogs={videoBlogs}
              loading={loading}
              pagination={pagination}
              onPageChange={handlePageChange}
              onDelete={handleDelete}
              onEdit={(id) => router.push(`/video-blogs/${id}/edit`)}
            />
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
